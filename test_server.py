"""
测试MCP优化服务器的功能
"""

import asyncio
import json
from fastmcp import Client
from optimized_server import server

async def test_server_functions():
    """测试服务器功能"""
    print("开始测试MCP优化服务器...")
    
    try:
        # 使用内存传输连接到服务器
        async with <PERSON><PERSON>(server.mcp) as client:
            print("✅ 成功连接到服务器")
            
            # 测试获取工具列表
            tools = await client.list_tools()
            print(f"✅ 可用工具数量: {len(tools.tools)}")
            for tool in tools.tools:
                print(f"   - {tool.name}: {tool.description}")
            
            # 测试文本处理优化
            print("\n🧪 测试文本处理优化...")
            result = await client.call_tool("process_text_optimized", {
                "text": "这是一个测试文本，用于验证缓存功能。"
            })
            print(f"   结果: {result.content[0].text}")
            
            # 再次处理相同文本（应该命中缓存）
            print("\n🧪 测试缓存命中...")
            result2 = await client.call_tool("process_text_optimized", {
                "text": "这是一个测试文本，用于验证缓存功能。"
            })
            print(f"   结果: {result2.content[0].text}")
            
            # 测试批量处理
            print("\n🧪 测试批量处理...")
            batch_result = await client.call_tool("process_batch_optimized", {
                "texts": [
                    "文本1：测试批量处理功能",
                    "文本2：验证优化效果",
                    "文本3：检查缓存机制"
                ]
            })
            print(f"   批量处理结果: {batch_result.content[0].text}")
            
            # 测试保存对话记忆
            print("\n🧪 测试保存对话记忆...")
            memory_result = await client.call_tool("save_conversation_memory", {
                "session_id": "test_session_001",
                "user_message": "你好，这是一个测试消息",
                "assistant_response": "你好！我是MCP优化服务器，很高兴为您服务。"
            })
            print(f"   保存结果: {memory_result.content[0].text}")
            
            # 测试获取对话记忆
            print("\n🧪 测试获取对话记忆...")
            history_result = await client.call_tool("get_conversation_memory", {
                "session_id": "test_session_001"
            })
            print(f"   历史记录: {history_result.content[0].text}")
            
            # 测试获取缓存统计
            print("\n🧪 测试获取缓存统计...")
            stats_result = await client.call_tool("get_cache_stats", {})
            print(f"   统计信息: {stats_result.content[0].text}")
            
            print("\n✅ 所有测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_server_functions())
