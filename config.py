"""
配置文件 - MCP优化服务器配置
"""

import os
from typing import Dict, Any

# 数据库配置
DATABASE_CONFIG = {
    "db_path": "mcp_memory.db",
    "conversation_table": "conversations",
    "cache_table": "text_cache"
}

# 优化配置
OPTIMIZATION_CONFIG = {
    "max_cache_size": 10000,  # 最大缓存条目数
    "cache_ttl": 3600,  # 缓存生存时间（秒）
    "batch_size": 50,  # 批处理大小
    "max_text_length": 10000,  # 最大文本长度
    "enable_deduplication": True,  # 启用去重
    "compression_threshold": 1000,  # 压缩阈值
}

# 服务器配置
SERVER_CONFIG = {
    "name": "MCP优化服务器",
    "version": "1.0.0",
    "description": "用于优化大模型API请求次数并提供记忆功能的MCP服务器",
    "host": "127.0.0.1",
    "port": 8000,
    "sse_path": "/sse",
    "studio_support": True,
    "remote_support": True
}

# 记忆配置
MEMORY_CONFIG = {
    "max_conversation_history": 1000,  # 最大对话历史条目数
    "context_window_size": 10,  # 上下文窗口大小
    "memory_compression": True,  # 启用记忆压缩
    "auto_cleanup_days": 30,  # 自动清理天数
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "mcp_server.log"
}

def get_config() -> Dict[str, Any]:
    """获取完整配置"""
    return {
        "database": DATABASE_CONFIG,
        "optimization": OPTIMIZATION_CONFIG,
        "server": SERVER_CONFIG,
        "memory": MEMORY_CONFIG,
        "logging": LOGGING_CONFIG
    }
