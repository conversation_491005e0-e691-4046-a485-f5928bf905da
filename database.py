"""
数据库管理模块 - 处理对话历史和缓存存储
"""

import sqlite3
import json
import hashlib
import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging
from config import DATABASE_CONFIG, MEMORY_CONFIG, OPTIMIZATION_CONFIG

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or DATABASE_CONFIG["db_path"]
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建对话历史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        user_message TEXT NOT NULL,
                        assistant_response TEXT NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        message_hash TEXT UNIQUE,
                        context_data TEXT,
                        token_count INTEGER DEFAULT 0
                    )
                """)
                
                # 创建文本缓存表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS text_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        text_hash TEXT UNIQUE NOT NULL,
                        original_text TEXT NOT NULL,
                        processed_result TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        access_count INTEGER DEFAULT 1,
                        last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_session_id ON conversations(session_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON conversations(timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_text_hash ON text_cache(text_hash)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_last_accessed ON text_cache(last_accessed)")
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_text_hash(self, text: str) -> str:
        """生成文本哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def save_conversation(self, session_id: str, user_message: str, 
                         assistant_response: str, context_data: Dict = None,
                         token_count: int = 0) -> bool:
        """保存对话记录"""
        try:
            message_hash = self.get_text_hash(user_message + assistant_response)
            context_json = json.dumps(context_data) if context_data else None
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO conversations 
                    (session_id, user_message, assistant_response, message_hash, context_data, token_count)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (session_id, user_message, assistant_response, message_hash, context_json, token_count))
                conn.commit()
                
            logger.info(f"对话记录已保存: session_id={session_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存对话记录失败: {e}")
            return False
    
    def get_conversation_history(self, session_id: str, limit: int = None) -> List[Dict]:
        """获取对话历史"""
        try:
            limit = limit or MEMORY_CONFIG["max_conversation_history"]
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT user_message, assistant_response, timestamp, context_data, token_count
                    FROM conversations 
                    WHERE session_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (session_id, limit))
                
                rows = cursor.fetchall()
                
                history = []
                for row in rows:
                    context_data = json.loads(row[3]) if row[3] else {}
                    history.append({
                        "user_message": row[0],
                        "assistant_response": row[1],
                        "timestamp": row[2],
                        "context_data": context_data,
                        "token_count": row[4]
                    })
                
                # 按时间正序返回
                return list(reversed(history))
                
        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return []
    
    def cache_text_result(self, text: str, result: str) -> bool:
        """缓存文本处理结果"""
        try:
            text_hash = self.get_text_hash(text)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在
                cursor.execute("SELECT id FROM text_cache WHERE text_hash = ?", (text_hash,))
                if cursor.fetchone():
                    # 更新访问计数和时间
                    cursor.execute("""
                        UPDATE text_cache 
                        SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                        WHERE text_hash = ?
                    """, (text_hash,))
                else:
                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO text_cache (text_hash, original_text, processed_result)
                        VALUES (?, ?, ?)
                    """, (text_hash, text, result))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"缓存文本结果失败: {e}")
            return False
    
    def get_cached_result(self, text: str) -> Optional[str]:
        """获取缓存的文本处理结果"""
        try:
            text_hash = self.get_text_hash(text)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT processed_result FROM text_cache 
                    WHERE text_hash = ?
                """, (text_hash,))
                
                row = cursor.fetchone()
                if row:
                    # 更新访问计数和时间
                    cursor.execute("""
                        UPDATE text_cache 
                        SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                        WHERE text_hash = ?
                    """, (text_hash,))
                    conn.commit()
                    return row[0]
                
                return None
                
        except Exception as e:
            logger.error(f"获取缓存结果失败: {e}")
            return None
    
    def cleanup_old_cache(self, days: int = None) -> int:
        """清理旧缓存"""
        try:
            days = days or MEMORY_CONFIG["auto_cleanup_days"]
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    DELETE FROM text_cache 
                    WHERE last_accessed < ?
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧缓存记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 缓存总数
                cursor.execute("SELECT COUNT(*) FROM text_cache")
                total_cache = cursor.fetchone()[0]
                
                # 对话总数
                cursor.execute("SELECT COUNT(*) FROM conversations")
                total_conversations = cursor.fetchone()[0]
                
                # 今日缓存命中数
                today = datetime.date.today()
                cursor.execute("""
                    SELECT COUNT(*) FROM text_cache 
                    WHERE DATE(last_accessed) = ?
                """, (today,))
                today_hits = cursor.fetchone()[0]
                
                return {
                    "total_cache_entries": total_cache,
                    "total_conversations": total_conversations,
                    "today_cache_hits": today_hits,
                    "database_path": self.db_path
                }
                
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
