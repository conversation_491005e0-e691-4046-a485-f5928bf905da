"""
MCP优化服务器 - 减少大模型API请求次数并提供记忆功能
"""

import asyncio
import json
import logging
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid

from fastmcp import FastMCP, Context
from database import DatabaseManager
from config import get_config

# 配置日志
config = get_config()
logging.basicConfig(
    level=getattr(logging, config["logging"]["level"]),
    format=config["logging"]["format"],
    handlers=[
        logging.FileHandler(config["logging"]["file"]),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class OptimizedMCPServer:
    """优化的MCP服务器"""
    
    def __init__(self):
        self.config = get_config()
        self.db = DatabaseManager()
        self.mcp = FastMCP(self.config["server"]["name"])
        self.session_cache = {}  # 会话级缓存
        self.setup_tools()
        
        logger.info("MCP优化服务器初始化完成")
    
    def setup_tools(self):
        """设置MCP工具"""
        
        @self.mcp.tool()
        async def process_text_optimized(text: str, ctx: Context) -> Dict[str, Any]:
            """
            处理单个文本，优化token使用
            
            Args:
                text: 要处理的文本
                
            Returns:
                处理结果和优化信息
            """
            try:
                # 检查文本长度
                if len(text) > self.config["optimization"]["max_text_length"]:
                    return {
                        "error": f"文本长度超过限制 ({self.config['optimization']['max_text_length']})",
                        "optimized": False
                    }
                
                # 检查缓存
                cached_result = self.db.get_cached_result(text)
                if cached_result:
                    await ctx.info("使用缓存结果，节省API调用")
                    return {
                        "result": cached_result,
                        "cached": True,
                        "optimized": True,
                        "token_saved": len(text.split())
                    }
                
                # 模拟文本处理（实际应用中这里会调用大模型API）
                processed_text = f"已处理: {text[:100]}..." if len(text) > 100 else f"已处理: {text}"
                
                # 缓存结果
                self.db.cache_text_result(text, processed_text)
                
                await ctx.info(f"文本处理完成，已缓存结果")
                
                return {
                    "result": processed_text,
                    "cached": False,
                    "optimized": True,
                    "token_count": len(text.split())
                }
                
            except Exception as e:
                logger.error(f"文本处理失败: {e}")
                return {"error": str(e), "optimized": False}
        
        @self.mcp.tool()
        async def process_batch_optimized(texts: List[str], ctx: Context) -> Dict[str, Any]:
            """
            批量处理文本，减少请求次数
            
            Args:
                texts: 要处理的文本列表
                
            Returns:
                批量处理结果
            """
            try:
                if len(texts) > self.config["optimization"]["batch_size"]:
                    return {
                        "error": f"批量大小超过限制 ({self.config['optimization']['batch_size']})",
                        "optimized": False
                    }
                
                results = []
                cached_count = 0
                total_tokens = 0
                
                await ctx.info(f"开始批量处理 {len(texts)} 个文本")
                
                for i, text in enumerate(texts):
                    # 检查缓存
                    cached_result = self.db.get_cached_result(text)
                    if cached_result:
                        results.append({
                            "index": i,
                            "result": cached_result,
                            "cached": True
                        })
                        cached_count += 1
                    else:
                        # 模拟处理
                        processed = f"批量处理[{i}]: {text[:50]}..." if len(text) > 50 else f"批量处理[{i}]: {text}"
                        self.db.cache_text_result(text, processed)
                        results.append({
                            "index": i,
                            "result": processed,
                            "cached": False
                        })
                    
                    total_tokens += len(text.split())
                
                await ctx.info(f"批量处理完成，缓存命中: {cached_count}/{len(texts)}")
                
                return {
                    "results": results,
                    "total_processed": len(texts),
                    "cached_hits": cached_count,
                    "cache_hit_rate": cached_count / len(texts) if texts else 0,
                    "total_tokens": total_tokens,
                    "optimized": True
                }
                
            except Exception as e:
                logger.error(f"批量处理失败: {e}")
                return {"error": str(e), "optimized": False}
        
        @self.mcp.tool()
        async def save_conversation_memory(
            session_id: str, 
            user_message: str, 
            assistant_response: str,
            ctx: Context
        ) -> Dict[str, Any]:
            """
            保存对话到记忆系统
            
            Args:
                session_id: 会话ID
                user_message: 用户消息
                assistant_response: 助手回复
                
            Returns:
                保存结果
            """
            try:
                token_count = len(user_message.split()) + len(assistant_response.split())
                
                success = self.db.save_conversation(
                    session_id=session_id,
                    user_message=user_message,
                    assistant_response=assistant_response,
                    context_data={"timestamp": datetime.now().isoformat()},
                    token_count=token_count
                )
                
                if success:
                    await ctx.info(f"对话已保存到记忆系统: {session_id}")
                    return {
                        "saved": True,
                        "session_id": session_id,
                        "token_count": token_count
                    }
                else:
                    return {"saved": False, "error": "保存失败"}
                    
            except Exception as e:
                logger.error(f"保存对话记忆失败: {e}")
                return {"saved": False, "error": str(e)}
        
        @self.mcp.tool()
        async def get_conversation_memory(
            session_id: str,
            ctx: Context,
            limit: Optional[int] = None
        ) -> Dict[str, Any]:
            """
            获取对话记忆
            
            Args:
                session_id: 会话ID
                limit: 限制返回的对话数量
                
            Returns:
                对话历史
            """
            try:
                history = self.db.get_conversation_history(session_id, limit)
                
                await ctx.info(f"获取到 {len(history)} 条对话记忆")
                
                return {
                    "session_id": session_id,
                    "conversation_count": len(history),
                    "history": history,
                    "has_memory": len(history) > 0
                }
                
            except Exception as e:
                logger.error(f"获取对话记忆失败: {e}")
                return {"error": str(e), "has_memory": False}
        
        @self.mcp.tool()
        async def get_cache_stats(ctx: Context) -> Dict[str, Any]:
            """
            获取缓存统计信息
            
            Returns:
                缓存统计数据
            """
            try:
                stats = self.db.get_cache_stats()
                await ctx.info("缓存统计信息获取成功")
                return stats
                
            except Exception as e:
                logger.error(f"获取缓存统计失败: {e}")
                return {"error": str(e)}
        
        @self.mcp.tool()
        async def clear_cache(ctx: Context) -> Dict[str, Any]:
            """
            清理缓存
            
            Returns:
                清理结果
            """
            try:
                deleted_count = self.db.cleanup_old_cache()
                await ctx.info(f"缓存清理完成，删除了 {deleted_count} 条记录")
                
                return {
                    "cleared": True,
                    "deleted_count": deleted_count
                }
                
            except Exception as e:
                logger.error(f"清理缓存失败: {e}")
                return {"cleared": False, "error": str(e)}
        
        @self.mcp.tool()
        async def optimize_config(
            ctx: Context,
            batch_size: Optional[int] = None,
            max_text_length: Optional[int] = None,
            enable_deduplication: Optional[bool] = None
        ) -> Dict[str, Any]:
            """
            动态调整优化配置
            
            Args:
                batch_size: 批处理大小
                max_text_length: 最大文本长度
                enable_deduplication: 启用去重
                
            Returns:
                配置更新结果
            """
            try:
                updates = {}
                
                if batch_size is not None:
                    self.config["optimization"]["batch_size"] = batch_size
                    updates["batch_size"] = batch_size
                
                if max_text_length is not None:
                    self.config["optimization"]["max_text_length"] = max_text_length
                    updates["max_text_length"] = max_text_length
                
                if enable_deduplication is not None:
                    self.config["optimization"]["enable_deduplication"] = enable_deduplication
                    updates["enable_deduplication"] = enable_deduplication
                
                await ctx.info(f"配置已更新: {updates}")
                
                return {
                    "updated": True,
                    "changes": updates,
                    "current_config": self.config["optimization"]
                }
                
            except Exception as e:
                logger.error(f"配置更新失败: {e}")
                return {"updated": False, "error": str(e)}
    
    def run_stdio(self):
        """运行STDIO模式（本地studio支持）"""
        logger.info("启动STDIO模式服务器")
        self.mcp.run(transport="stdio")
    
    def run_sse(self, host: str = None, port: int = None):
        """运行SSE模式（远程连接支持）"""
        host = host or self.config["server"]["host"]
        port = port or self.config["server"]["port"]
        
        logger.info(f"启动SSE模式服务器: {host}:{port}")
        self.mcp.run(transport="sse", host=host, port=port)

# 创建服务器实例
server = OptimizedMCPServer()

if __name__ == "__main__":
    import sys
    
    # 根据命令行参数选择运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "sse":
        # SSE模式 - 远程连接
        server.run_sse()
    else:
        # STDIO模式 - 本地studio
        server.run_stdio()
