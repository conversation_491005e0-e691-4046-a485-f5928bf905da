# MCP优化服务器关键词文件
# 供MCP客户端调用的工具和功能关键词

## 核心功能关键词
优化文本处理
批量处理
缓存管理
记忆功能
对话历史
API优化
token节省

## 工具名称
process_text_optimized
process_batch_optimized
save_conversation_memory
get_conversation_memory
get_cache_stats
clear_cache
optimize_config

## 连接方式
本地连接: python optimized_server.py
远程连接: python optimized_server.py sse
SSE端点: http://127.0.0.1:8000/sse

## 主要特性
- 智能缓存系统，避免重复API调用
- 批量处理减少请求次数
- 对话记忆持久化存储
- 支持本地studio和远程SSE连接
- 动态配置优化参数
- 详细的统计和监控

## 使用场景
文本去重处理
批量内容分析
对话上下文维护
API成本优化
大模型请求优化

## 配置参数
batch_size: 批处理大小
max_text_length: 最大文本长度
enable_deduplication: 启用去重
cache_ttl: 缓存生存时间
max_conversation_history: 最大对话历史

## 数据库表
conversations: 对话历史表
text_cache: 文本缓存表

## 优化效果
- 减少重复API调用
- 降低token消耗
- 提高响应速度
- 维持对话连续性
- 智能缓存命中
