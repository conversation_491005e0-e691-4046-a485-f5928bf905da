"""
简单测试MCP服务器基本功能
"""

from database import DatabaseManager
from config import get_config

def test_database():
    """测试数据库功能"""
    print("测试数据库功能...")
    
    db = DatabaseManager()
    
    # 测试缓存功能
    print("1. 测试缓存功能")
    text = "这是一个测试文本"
    result = "处理后的文本结果"
    
    # 保存到缓存
    success = db.cache_text_result(text, result)
    print(f"   缓存保存: {'成功' if success else '失败'}")
    
    # 从缓存获取
    cached = db.get_cached_result(text)
    print(f"   缓存获取: {'成功' if cached == result else '失败'}")
    print(f"   缓存内容: {cached}")
    
    # 测试对话记忆
    print("\n2. 测试对话记忆")
    session_id = "test_session"
    user_msg = "你好"
    assistant_msg = "你好！有什么可以帮助您的吗？"
    
    # 保存对话
    success = db.save_conversation(session_id, user_msg, assistant_msg)
    print(f"   对话保存: {'成功' if success else '失败'}")
    
    # 获取对话历史
    history = db.get_conversation_history(session_id)
    print(f"   历史获取: {'成功' if len(history) > 0 else '失败'}")
    print(f"   历史条数: {len(history)}")
    if history:
        print(f"   最新对话: {history[-1]['user_message']} -> {history[-1]['assistant_response']}")
    
    # 获取统计信息
    print("\n3. 测试统计信息")
    stats = db.get_cache_stats()
    print(f"   统计信息: {stats}")
    
    print("\n✅ 数据库测试完成！")

def test_config():
    """测试配置功能"""
    print("\n测试配置功能...")
    
    config = get_config()
    print(f"服务器名称: {config['server']['name']}")
    print(f"数据库路径: {config['database']['db_path']}")
    print(f"批处理大小: {config['optimization']['batch_size']}")
    print(f"最大缓存大小: {config['optimization']['max_cache_size']}")
    
    print("✅ 配置测试完成！")

if __name__ == "__main__":
    test_config()
    test_database()
